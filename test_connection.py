#!/usr/bin/env python3
"""
Test script to verify WatsonX Discovery connection and run a simple spider.
"""

import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_elasticsearch_connection():
    """Test connection to WatsonX Discovery/Elasticsearch"""
    try:
        from wxdingest.elasticsearch_interface import ElasticsearchInterface
        
        print("🔍 Testing WatsonX Discovery connection...")
        print(f"URL: {os.getenv('ELASTICSEARCH_URL')}")
        print(f"Username: {os.getenv('ELASTICSEARCH_USER_NAME')}")
        
        # Create Elasticsearch interface
        es_interface = ElasticsearchInterface()
        es_interface.set_client()
        
        # Test connection by getting cluster info
        info = es_interface.client.info()
        print("✅ Connection successful!")
        print(f"Cluster name: {info.body.get('cluster_name', 'Unknown')}")
        print(f"Version: {info.body.get('version', {}).get('number', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def list_available_spiders():
    """List all available spider configurations"""
    try:
        from wxdingest.ingest.ingest_controller import IngestController
        
        controller = IngestController()
        configs = controller.list_config_names()
        
        print(f"\n📋 Available spider configurations:")
        for config in configs:
            print(f"  - {config}")
            
        return configs
        
    except Exception as e:
        print(f"❌ Could not list spider configs: {e}")
        return []

def run_test_spider():
    """Run the test spider"""
    try:
        from wxdingest.ingest.ingest_controller import IngestController

        print("\n🕷️  Starting test spider...")
        print("This will crawl a small test configuration.")
        print("Press Ctrl+C to stop if needed.\n")

        # For local configs, we need to pass the full path to the config file
        config_path = os.path.join(os.getenv('CONFIGS_BASEPATH', 'config'), 'spider_configs', 'test.json')

        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            print("Creating a test config file...")

            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(config_path), exist_ok=True)

            # Create a simple test config
            test_config = {
                "name": "test",
                "index": "test-index-1",
                "pipeline": "english",
                "locale": None,
                "allowed_domains": ["httpbin.org"],
                "seeds": [
                    "https://httpbin.org/html"
                ],
                "depth": 0,  # Only crawl seed URLs
                "exclude": [],
                "allow": [],
                "no-follow": [],
                "no-index": [],
                "split": [],
                "dynamic": False
            }

            with open(config_path, 'w') as f:
                json.dump(test_config, f, indent=2)

            print(f"✅ Created test config: {config_path}")

        controller = IngestController()
        controller.ingest(config_path)  # Pass full path for local configs

        print("✅ Spider completed successfully!")

    except KeyboardInterrupt:
        print("\n⏹️  Spider stopped by user")
    except Exception as e:
        print(f"❌ Spider failed: {e}")
        print(f"Error details: {str(e)}")
        print("\nTroubleshooting tips:")
        print("1. Make sure your .env file has correct credentials")
        print("2. Check if the spider configuration file exists")
        print("3. Verify network connectivity to WatsonX Discovery")
        print("4. Check the logs above for more specific error messages")

def main():
    """Main function to run tests"""
    print("🚀 WatsonX Discovery Ingest - Connection Test\n")
    
    # Test 1: Connection
    if not test_elasticsearch_connection():
        print("\n❌ Cannot proceed without a valid connection.")
        print("Please check your credentials in the .env file.")
        sys.exit(1)
    
    # Test 2: List spiders
    configs = list_available_spiders()
    
    # Test 3: Ask user if they want to run a spider
    if configs:
        response = input(f"\n🤔 Would you like to run the 'test' spider? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            run_test_spider()
        else:
            print("👍 Connection test completed. You can run spiders manually when ready.")
    else:
        print("\n⚠️  No spider configurations found. You may need to:")
        print("1. Set LOCAL_CONFIGS=true in your .env file")
        print("2. Create spider configuration files in the config directory")
        print("3. Or upload configurations to WatsonX Discovery")

if __name__ == "__main__":
    main()
