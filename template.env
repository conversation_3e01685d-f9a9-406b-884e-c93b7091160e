

# ELA<PERSON><PERSON><PERSON><PERSON><PERSON> INSTANCES
# The ingest code that is run in the Docker container
# will always refer to the default instance that is configured 
# in environment variables. This will also be the default if no
# instance name is provided or if no instances.json exists.

# DEV
ELASTICSEARCH_URL=https://wx-discovery.ccca.ams1907.com
ELASTICSEARCH_USER_NAME=<REPLACE>
ELASTICSEARCH_PASSWORD=<REPLACE>

# Path to instances.json that holds credentials for all environments
INSTANCES=<REPLACE>/instances.json


# ELASTICSEARCH BASE CONFIGURATION
# This defines the name of the ML models and fields
# as they are used when running searches. It needs
# to match the name of the models as deployed ini ES.

# Model names
E5_MODEL_NAME=.multilingual-e5-small_linux-x86_64
E5_FIELD_NAME=e5.predicted_value

# Field names
ELSER_MODEL_NAME=.elser_model_2_linux-x86_64
ELSER_FIELD_NAME=elser.predicted_value


# INGESTION PARAMETERS
# Parameters used during ingestion

# Control the purging of documents as described in the README
STALE_DAYS=10
PURGE=true


# DEVELOPMENT AND TESTING
# This sets paths to various directories needed to 
# perform various actions.

# Used by ingest and setup to find configuration artifacts,
# such as language files, pipelines, and index mappings.
# This is also used by the container to find configuration files.
CONFIGS_BASEPATH=<REPLACE>/config

# When we ingest, we pull spider configs from the Elasticsearch 
# spider-configs index. But for testing, we might want to use a local
# config file. This allows us to do that.
LOCAL_CONFIGS=false