import re
import xml.etree.ElementTree as ET
from typing import AsyncIterable, Any, List, Set
from datetime import datetime
import logging
logger = logging.getLogger(__name__)

import scrapy
from scrapy.spiders import CrawlSpider, Rule
from scrapy.linkextractors import LinkExtractor
from scrapy.http import Response

from wxdingest.ingest.page_processor import PageProcessor
from wxdingest.setup.spider_config_manager import SpiderConfigManager
from wxdingest.model import SpiderConfig, IngestDocument
from wxdingest import config
from wxdingest.ingest.deploy.ups_ingest.items import UpsIngestItem

from scrapy.linkextractors import IGNORED_EXTENSIONS

class TemplateSpider(CrawlSpider):

    processor = PageProcessor()
    config_manager = SpiderConfigManager()

    def __init__(self, *args, **kwargs):
        """
        This sets the spider configuration based on the passed
        in configuration object.
        """
        spider_config: SpiderConfig = self._load_crawler_config(kwargs)
        if spider_config is None:
            logger.error(f'ERROR - crawler config could not be loaded')
            return
        
        # If we don't set the name here, we can't do self.log()
        TemplateSpider.name = spider_config.name
        
        self.log(f"Custom Settings: {kwargs}", level=logging.INFO)
        
        # Apply config values to spider
        TemplateSpider.index = spider_config.index
        
        # Overwrite if passed in via UI
        if 'index' in kwargs:
            TemplateSpider.index = kwargs.get('index', TemplateSpider.index)
            self.log(f"Index set to {TemplateSpider.index} based on command line parameter", level=logging.INFO)
            
        # Only purge documents if we don't explicitly disallow it
        TemplateSpider.purge_old_pages:bool = config.get_parameter("PURGE", True)
        # ... overwrite if passed in via UI
        TemplateSpider.purge_old_pages:bool = kwargs.get('purge', str(TemplateSpider.purge_old_pages)).lower() in ['true', '1']
        self.log(f"Purge pages: {TemplateSpider.purge_old_pages}", level=logging.INFO)
        
        # Purge documents that are older than specific number of days
        TemplateSpider.stale_purge_days = config.get_parameter("STALE_DAYS", 10)
        # ... overwrite if passed in via UI
        try:
            TemplateSpider.stale_purge_days = int(kwargs.get('stale_days', TemplateSpider.stale_purge_days))
        except Exception as e:
            self.log(f"Error reading the stale_days parameter: {e}", level=logging.WARNING)
        self.log(f"Stale days set to {TemplateSpider.stale_purge_days}", level=logging.INFO)
        
        # Even if the document has not been updated, overwrite it
        TemplateSpider.force_doc_refresh:bool = config.get_parameter("FORCE_REFRESH", False)
        # ... overwrite if passed in via UI
        TemplateSpider.force_doc_refresh = kwargs.get('force_refresh', str(TemplateSpider.force_doc_refresh)).lower() in ['true', '1']
        self.log(f"Force page refresh: {TemplateSpider.force_doc_refresh}", level=logging.INFO)
        
        # Timeout for indexing documents in watsonx Discovery
        wxd_timeout_args = kwargs.get('wxd_timeout')
        if wxd_timeout_args is not None:
            TemplateSpider.processor.elastic.set_client(timeout=int(wxd_timeout_args))
            self.log(f"wxD Timeout from arguments: {wxd_timeout_args}", level=logging.INFO)
        else:
            # ... try to load it from the environment
            wxd_timeout_env:int = config.get_parameter("WXD_TIMEOUT")
            if wxd_timeout_env is not None:
                TemplateSpider.processor.elastic.set_client(timeout=int(wxd_timeout_env))
                self.log(f"wxD Timeout from environment: {wxd_timeout_env}", level=logging.INFO)
        
        TemplateSpider.pipeline = spider_config.pipeline
        TemplateSpider.locale = spider_config.locale
        TemplateSpider.allowed_domains = spider_config.allowed_domains
        TemplateSpider.start_urls = spider_config.seeds
        TemplateSpider.no_index = spider_config.no_index
        TemplateSpider.no_follow = spider_config.no_follow
        TemplateSpider.split = spider_config.split
        
        # Only crawl seed pages if depth is set to 0
        if spider_config.depth <=0:
            self.log("Depth is set to 0. Skipping follow.", level=logging.INFO)
        else:
            # If depth > 0, define extraction rules

            # Make sure that PDF document links are not rejected
            accept_extensions:List[str] = ["pdf"]
            deny_extensions:Set[str] = set([e for e in IGNORED_EXTENSIONS if e not in accept_extensions])
            
            TemplateSpider.rules = (
                Rule(
                    LinkExtractor(
                        allow=tuple(spider_config.allow),
                        deny=tuple(spider_config.exclude),
                        deny_extensions=deny_extensions
                    ),
                    callback='parse_item',
                    process_request="handle_request",
                    process_links='process_links',
                    follow=True
                ),
            )
            
        # Needed for purging documents
        self.crawl_start_time = datetime.utcnow()
        
        super().__init__(*args, **kwargs)
        

    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        spider = super().from_crawler(crawler, *args, **kwargs)
        # DEPTH_LIMIT needs to be set here
        spider_config: SpiderConfig = spider._load_crawler_config(kwargs)
        spider.settings.set("DEPTH_LIMIT", spider_config.depth, priority="spider")
        spider.settings.set("BOT_NAME", spider_config.name, priority="spider")
        
        # Print out headers so we know how we are calling sites.
        try:
            headers = {k:v for k,v in spider.settings["DEFAULT_REQUEST_HEADERS"].items()}
        except:
            headers = {}
        spider.log(f"Crawler Request Headers: {headers}", level=logging.INFO)
        return spider
    
    
    def handle_request(self, request, spider):
        """
        This sets the metadata for a request, such as playright
        or cookies.
        """
        # Addresses 400 error due to cookies
        # We are handling this by setting COOKIES_ENABLED to False
        # in the settings file. If it works, we can remove this.
        # request.meta['dont_merge_cookies'] = True
        return request

    def parse_start_url(self, response):
        # If the first page is XML, parse as sitemap
        content_type = response.headers.get(
            'Content-Type', b'').decode('utf-8').lower()
        if 'xml' in content_type or response.url.endswith('.xml'):
            return self.parse_sitemap(response)
        
        # If we don't explicitly parse the start URLs, their
        # content won't be extracted
        return self.parse_item(response)

    def parse_sitemap(self, response):
        try:
            root = ET.fromstring(response.body)
        except ET.ParseError as e:
            self.log(f"Could not parse sitemap XML: {e}", level=logging.WARNING)
            return

        ns = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}

        # check for sitemap index
        for sitemap_el in root.findall('sm:sitemap', ns):
            loc_el = sitemap_el.find('sm:loc', ns)
            if loc_el is not None:
                yield scrapy.Request(loc_el.text)

        # check for url set
        for url_el in root.findall('sm:url', ns):
            loc_el = url_el.find('sm:loc', ns)
            if loc_el is not None:
                yield scrapy.Request(loc_el.text)
                
    def process_links(self, links):
        """
        Filters links that we don't want to follow.
        """
        for link in links:
            if self.matches_any_regex(link.url, TemplateSpider.no_follow):
                link.nofollow = True
                self.log(f"Marking {link.url} as no-follow because it is on the no_follow list", level=logging.INFO)
                
            # Process URL before we yield the link
            link.url = self.evaluate_link(link.url)
            yield link
        
    def evaluate_link(self, url) -> str:
        """
        Applies any pre-processing, such as splitting. Returns
        the processed URL.
        """
            
        # Split URL if so desired
        for s in self.split:
            if s in url:
                orig_url:str = url + ""
                url = url.split(s)[0].rstrip('&').rstrip('?')
                self.log(f"Removed {s} from {orig_url} | new URL: {url}", level=logging.INFO)
                break # only apply the first splitting if multiple match
            
        # Return the link object
        return url 

    def matches_any_regex(self, url, regex_list):
        for pattern in regex_list:
            if re.match(pattern, url):
                return True
        return False
    
    def parse_item(self, response):
        
        # Check if this URL should have been split. If this comes
        # from a redirect, it doesn't pass through the parse_links
        # functions and ends up here. Now, we have the chance to stop
        # processing such links and yield the correct URL for processing.
        url:str = self.evaluate_link(response.url)
        if url != response.url:
            yield scrapy.Request(url)
            return
        
        # Don't parse pages on the 'no_index' list
        if self.matches_any_regex(response.url, TemplateSpider.no_index):
            self.log(f"Skipping {response.url} because it is on the no_index list", level=logging.INFO)
            return

        # If XML file, parse as sitemap
        content_type = response.headers.get(
            'Content-Type', b'').decode('utf-8').lower()
        if 'xml' in content_type:
            yield from self.parse_sitemap(response)
        else:
            doc:IngestDocument = self.processor.process_page(response, self.index, self.pipeline, locale_default=self.locale, log=self.log, force_refresh=self.force_doc_refresh)
            
            # Create an ingest item so that we can monitor the production
            # of items in ScrapydWeb
            if doc is not None:
                item:UpsIngestItem = UpsIngestItem()
                item['url'] = doc.url
                item['title'] = doc.title
                item['locale'] = doc.locale
                item['description'] = doc.description
                item['body'] = doc.body
                item['latency'] = doc.latency
                item['ingest_duration'] = doc.ingest_duration
                item['depth'] = doc.depth
                item['referrer'] = doc.referrer
                item['ingest_action'] = doc.ingest_action
                
                # Log the item explicitly if DEBUG is not already printing it
                try:
                    if self.settings['LOG_LEVEL'] != "DEBUG":
                        self.log(item, level=logging.INFO)
                except:
                    pass
                
                # Yield the item so it shows up as item in the crawler.
                yield item
        
    def _load_crawler_config(self, kwargs) -> SpiderConfig:
        """
        Loads the crawler config to apply them to the spider.
        """
        config_name:str = None
        if 'config_name' in kwargs:
            # The config_path is set if the TemplateSpider is invoked
            config_name = kwargs['config_name']
        else:
            # If a sub-spider is invoked by scrapyd, self.CONFIG_NAME is set
            try:
                config_name = self.CONFIG_NAME
            except:
                pass
        
        return self.config_manager.load_spider_config(config_name)
    
    def closed(self, reason):
        """
        When the crawler terminates, we purge old pages.
        """
        self.processor.purge_old_pages(self.crawl_start_time,self.index, stale_days=self.stale_purge_days, purge=self.purge_old_pages)

    async def _parse_response(
        self,
        response: Response,
        callback,
        cb_kwargs: dict[str, Any],
        follow: bool = True,
    ) -> AsyncIterable[Any]:
        if self.matches_any_regex(response.url, TemplateSpider.no_follow):
            follow = False
            self.log(f"Skipping parsing links for {response.url} because referrer is on the no_follow list", level=logging.INFO)
        async for item_or_request in super()._parse_response(
            response, callback, cb_kwargs, follow
        ):
            yield item_or_request
