
import hashlib
from datetime import datetime
from pypdf import PdfReader
from io import BytesIO

import logging
logger = logging.getLogger(__name__)

from wxdingest.model import IngestDocument
from wxdingest.ingest.ingest_utils import IngestUtils
from wxdingest.elasticsearch_interface import ElasticsearchInterface
from wxdingest.setup.instances_manager import InstancesManager
from wxdingest.ingest.pdf_description_generator import PDFDescriptionGenerator
from wxdingest import config

class PdfExtractor:
    """
    Extracts metadata and body from PDF documents.
    """
    
    def __init__(self, instance_name:str):
        self.utils = IngestUtils()
        self.elastic:ElasticsearchInterface = InstancesManager().wxd(instance_name)
        self.pdf_description_generator = PDFDescriptionGenerator()

    def extract(self, response, index:str) -> IngestDocument:
        try:
            pdf:PdfReader = PdfReader(BytesIO(response.body))
            # Extract metadata
            doc:IngestDocument = self._extract_document_metadata(response, pdf, index)
            # Extract body text
            doc.body = self._extract_body(pdf)
            # Extract description
            doc.description = self.pdf_description_generator.describe_single_pdf(doc)
            # Set the document type
            doc.file_type = "PDF"
            return doc
        except Exception as e:
            logger.warning(f"Could not parse PDF {response.url}: {e}")
    
    def _extract_body(self, pdf:PdfReader) -> str:
        return "\n".join([p.extract_text(extraction_mode="plain", layout_mode_space_vertically=False) for p in pdf.pages])
        
    def _extract_document_metadata(self, response,pdf:PdfReader, index:str) -> IngestDocument:
        """
        Parses the page HTML to extract the metadata fields
        as well as the body of the page.
        """
        doc = IngestDocument()
        doc.url = response.url
        doc.id = hashlib.md5(doc.url.encode('utf-8')).hexdigest()
        
        # Try to grab the information from the page HTML
        # Initialize fields - they are common across all document parts
        
        # TITLE
        try:
            doc.title = pdf.metadata.title
        except Exception as e:
            logger.info(f'Error with response Response: ({response.status}) "{response.text}": {e}')
            
        # DESCRIPTION
        if pdf.metadata.subject is not None:
            doc.description = pdf.metadata.subject
        else:
            doc.description = doc.title
        
        # KEYWORDS
        try:
            doc.keywords = pdf.metadata.keywords
        except:
            pass
        
        # DATE
        try:
            doc.published_time = pdf.metadata.creation_date.isoformat() + "Z"
        except:
            pass
        if not doc.published_time:
            doc.published_time = datetime.utcnow().isoformat() + "Z"
            
            
        # Locale - Extract it from referrer page since PDFs don't typically don't have a locale
        referrer_url:str = None
        try:
            referrer_url = response.request.headers.get('Referer', None)
            if referrer_url is None:
                logger.warning(f"Referrer url for {doc.url} is None. Can't extract locales")
            else:
                referrer_url = referrer_url.decode("utf-8")
        except Exception as e:
            logger.warning(f"Could not get referrer URL: {doc.url} -- {e}")
            
        if referrer_url is not None:
                
            try:
                ctry, lang = self.utils.get_country_and_language_from_url_and_locale(referrer_url, None)
                doc.locale = self.utils.generate_locale(ctry,lang)
            except Exception as e:
                logger.warning(f"Could not extract locale from PDF: {doc.url} | {referrer_url} -- {e}")
                
            # If we can't get the locale from the document itself, we'll grab it from the locale extracted
            # for the referrer, which is stored in wxD.
            if doc.locale is None:
                try:
                    referrer_id:str = hashlib.md5(referrer_url.encode('utf-8')).hexdigest()
                    jdoc:any = self.elastic.get_document_by_id(referrer_id, index, fields=["doc_id","locale"])
                    if jdoc is not None:
                        doc.locale = jdoc.get("locale")
                except Exception as e:
                    logger.warning(f"Could not extract locale from referrer URL: {doc.url} | {referrer_url} -- {e}")

        return doc