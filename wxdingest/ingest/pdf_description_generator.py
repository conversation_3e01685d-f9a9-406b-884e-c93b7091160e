import requests
import os
from langdetect import detect
import py3langid as langid
import logging

from wxdingest import config
from wxdingest.model import IngestDocument

from wxdingest.ingest.ingest_utils import IngestUtils


logger = logging.getLogger(__name__)

class PDFDescriptionGenerator:
    
    def __init__(self):

        # watsonx.ai connection information
        self.WATSONX_API_TOKEN = config.get_parameter("WATSONX_API_TOKEN")
        self.WATSONX_PROJECT_ID = config.get_parameter("WATSONX_PROJECT_ID")
        self.WATSONX_BASE_URL = config.get_parameter("WATSONX_BASE_URL")
        
        # Description parameters
        self.model_id:str = config.get_parameter("PDF_DESCRIPTION_MODEL", "ibm/granite-3-2-8b-instruct")
        self.parsed_pages:int = config.get_parameter("PDF_PAGES_COUNT", 3)

        self.utils = IngestUtils()
        langid.set_languages(list(self.utils.LANGUAGES))

    def _determine_content_language(self, content:str) -> str:
        try:
            langid_result = langid.classify(content)[0]
        except Exception as e:
            logger.exception(f"LangID classification failed: {e}")
            langid_result = ""
        langid_result = self.utils.LANGUAGE_NAMES[langid_result] if langid_result in self.utils.LANGUAGE_NAMES else ""

        try:
            langdetect_result = detect(content)
            if 'zh' in langdetect_result:
                langdetect_result = 'zh'
        except Exception as e:
            logger.exception(f"LangDetect classification failed: {e}")
            langdetect_result = ""
        langdetect_result = self.utils.LANGUAGE_NAMES[langdetect_result] if langdetect_result in self.utils.LANGUAGE_NAMES else ""

        granite_langid_result = ""
        if langid_result != langdetect_result:
            granite_langid_result = self._query_llm(prompt=self.utils.LANGID_PROMPT, content=content)[8:]
            return granite_langid_result if granite_langid_result else "English"
        else:
            return langid_result

    def _query_llm(self, prompt:str, content:str, content_language:str="English") -> str:
        url = f"{self.WATSONX_BASE_URL}/text/generation"

        headers = {}
        headers["Content-Type"] = "application/json"
        headers['Accept'] = 'application/json'
        headers["Authorization"] = f"Bearer {self.WATSONX_API_TOKEN}"

        params = {"version":"2021-05-01"}

        data = {
            "parameters": {
                "decoding_method": "greedy",
                "max_new_tokens": 300,
                "min_new_tokens": 0,
                "stop_sequences": [],
                "repetition_penalty": 1
            }
        }

        data["input"] = prompt.format(content_language=content_language.upper(), context=content)
        data["model_id"] = self.model_id
        data["project_id"] = self.WATSONX_PROJECT_ID

        try:
            response = requests.post(url, verify=False, json=data, headers=headers, params=params)
            response.raise_for_status()

            json_response = response.json()            
            if 'results' not in json_response:
                logger.error(f"No 'results' in LLM response: {json_response}")
                return ""
            
            cleaned_response = '\n'.join([r.get('generated_text', '').strip() for r in json_response['results']])
            return cleaned_response
        except requests.exceptions.RequestException as e:
            logger.exception(f"Request to WatsonX failed: {e}")
            return ""
        except ValueError as e:
            logger.exception(f"Failed to decode WatsonX response JSON: {e}")
            return ""

    def describe_single_pdf(self, doc:IngestDocument):
        contents = doc.body
        content_language = self._determine_content_language(content=contents)

        if doc.locale:
            try:
                lang, country_code = doc.locale.split("_")
            except ValueError as _:
                logger.error(f"Invalid locale format in doc: {doc.locale}")
                return doc.title

            if lang in self.utils.LANGUAGE_NAMES and country_code in self.utils.DIALECTS and lang in self.utils.DIALECTS[country_code]:
                if self.utils.LANGUAGE_NAMES[lang] == content_language:
                    content_language = self.utils.DIALECTS[country_code][lang]
                else:
                    logger.error(f"PDF LangID-Locale Mismatch | URL: {doc.url} | Language: {content_language} | Locale: {doc.locale}")
            else:
                if lang not in self.utils.LANGUAGE_NAMES:
                    logger.error(f"Unregistered Language Found | URL: {doc.url} | Language: {lang}")

                if country_code not in self.utils.DIALECTS:
                    logger.error(f"Unregistered Country Code | URL: {doc.url} | Country Code: {country_code}")
                else:
                    if lang not in self.utils.DIALECTS[country_code]:
                        logger.error(f"Unregistered Language for Country Code | URL: {doc.url} | Locale: {doc.locale}")

        description = self._query_llm(prompt=self.utils.DESCRIPTION_PROMPT, content=contents, content_language=content_language)[13:]

        if description:
            return description
        else:
            return doc.title