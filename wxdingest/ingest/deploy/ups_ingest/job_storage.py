"""
.. versionadded:: 1.3.0
   Job storage was previously in-memory only and managed by the launcher.
"""

from zope.interface import implementer

from scrapyd import sqlite
from scrapyd.interfaces import IJobStorage
from scrapyd.launcher import ScrapyProcessProtocol
from datetime import datetime
from wxdingest.utils.applog import get_logger
logger = get_logger(__name__)

from wxdingest import config
from wxdingest.elasticsearch_interface import ElasticsearchInterface
from wxdingest.setup.setup_controller import SetupController
            
@implementer(IJobStorage)
class ElasticJobStorage:
    def __init__(self, config):
        self.index_name:str = "scrapyd-jobs"
        self.index_mapping_name:str = "scrapyd-jobs"
        self.elastic = ElasticsearchInterface()
        self.setup = SetupController()
        self._check_index()
        self.jobs = self._load_jobs()
        
    def _load_jobs(self):
        wxd_query:any = {"query": { "match_all": {}}}
        response = self.elastic.query(query_body=wxd_query, index=self.index_name)
        try:
            jobs = []
            for hit in response.get('hits', {}).get('hits', []):
                jjob = hit.get('_source')
                project = jjob.get('project')
                spider = jjob.get('spider')
                job = jjob.get('jobid')
                start_time = jjob.get('start_time')
                end_time = jjob.get('end_time')
                job = ScrapyProcessProtocol(project,spider,job,{},{})
                job.start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S.%f")
                job.end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S.%f")
                jobs.append(job)
            return jobs
        except Exception as e:
            logger.error(f"Error loading crawler configuration from wxD: {e}")
            return []


    def add(self, job:ScrapyProcessProtocol):
        jjob = {"project":job.project, "spider":job.spider, "jobid":job.job, "start_time": job.start_time, "end_time": job.end_time}
        self.elastic.ingest_document(jjob, id=job.job, index=self.index_name)

    def list(self):
        return list(self.jobs)

    def __len__(self):
        return len(self.jobs)

    def __iter__(self):
        yield from reversed(self.jobs)
        
    def _check_index(self):
        """
        Checks if the index exists and, if not,
        creates a new index.
        """
        
        indices: List[str] = self.elastic.get_indices(index_name=self.index_name)
        logger.info(f"Indices: {indices}")
        
        if len(indices) <=0 :
            logger.info(
                f'Creating index {self.index_name} with mapping {self.index_mapping_name}')
            try:
                self.setup.create_index(
                    index_name=self.index_name, index_mapping=self.index_mapping_name)
            except Exception as e:
                logger.error(f"Error when creating index: {e}")

@implementer(IJobStorage)
class MemoryJobStorage:
    def __init__(self, config):
        self.jobs = []
        self.finished_to_keep = config.getint("finished_to_keep", 100)

    def add(self, job):
        self.jobs.append(job)
        del self.jobs[: -self.finished_to_keep]  # keep last x finished jobs

    def list(self):
        return list(self)

    def __len__(self):
        return len(self.jobs)

    def __iter__(self):
        yield from reversed(self.jobs)


@implementer(IJobStorage)
class SqliteJobStorage:
    def __init__(self, config):
        self.jobs = sqlite.initialize(sqlite.SqliteFinishedJobs, config, "jobs", "finished_jobs")
        self.finished_to_keep = config.getint("finished_to_keep", 100)

    def add(self, job):
        self.jobs.add(job)
        self.jobs.clear(self.finished_to_keep)

    def list(self):
        return list(self)

    def __len__(self):
        return len(self.jobs)

    def __iter__(self):
        for project, spider, jobid, start_time, end_time in self.jobs:
            job = ScrapyProcessProtocol(project, spider, jobid, env={}, args=[])
            job.start_time = start_time
            job.end_time = end_time
            yield job
            
if __name__ == "__main__":
    # ElasticJobStorage({})._check_index()
    for j in ElasticJobStorage({}):
        print(j)
    