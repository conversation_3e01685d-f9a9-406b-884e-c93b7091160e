# Scrapy settings for ups_ingest project
#
# For simplicity, this file contains only settings considered important or
# commonly used. You can find more settings consulting the documentation:
#
#     https://docs.scrapy.org/en/latest/topics/settings.html
#     https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
#     https://docs.scrapy.org/en/latest/topics/spider-middleware.html

import os

BOT_NAME = "ups_ingest"

SPIDER_MODULES = ["ups_ingest.spiders"]

# Crawl responsibly by identifying yourself (and your website) on the user-agent
# USER_AGENT = 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36 OPR/45.0.2552.888'
# USER_AGENT = 'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:47.0) Gecko/20100101 Firefox/47.3'

ENABLE_MONITOR = True
LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')

# Obey robots.txt rules
ROBOTSTXT_OBEY = False

# Configure maximum concurrent requests performed by Scrapy (default: 16)
CONCURRENT_REQUESTS = 50
try:
    CONCURRENT_REQUESTS = int(os.environ.get('CONCURRENT_REQUESTS',CONCURRENT_REQUESTS))
except:
    print(f"Error parsing CONCURRENT_REQUESTS: {CONCURRENT_REQUESTS}")


# Configure a delay for requests for the same website (default: 0)
# See https://docs.scrapy.org/en/latest/topics/settings.html#download-delay
# See also autothrottle settings and docs
# Configure maximum concurrent requests performed by Scrapy (default: 16)
DOWNLOAD_DELAY = 0.1
try:
    DOWNLOAD_DELAY = float(os.environ.get('DOWNLOAD_DELAY',DOWNLOAD_DELAY))
except:
    print(f"Error parsing DOWNLOAD_DELAY: {DOWNLOAD_DELAY}")


# The download delay setting will honor only one of:
CONCURRENT_REQUESTS_PER_DOMAIN = 50
try:
    CONCURRENT_REQUESTS_PER_DOMAIN = int(os.environ.get('CONCURRENT_REQUESTS_PER_DOMAIN',CONCURRENT_REQUESTS_PER_DOMAIN))
except:
    print(f"Error parsing CONCURRENT_REQUESTS_PER_DOMAIN: {CONCURRENT_REQUESTS_PER_DOMAIN}")
    
    
CONCURRENT_REQUESTS_PER_IP = 50
try:
    CONCURRENT_REQUESTS_PER_IP = int(os.environ.get('CONCURRENT_REQUESTS_PER_IP',CONCURRENT_REQUESTS_PER_IP))
except:
    print(f"Error parsing CONCURRENT_REQUESTS_PER_IP: {CONCURRENT_REQUESTS_PER_IP}")
    

# Disable cookies (enabled by default)
COOKIES_ENABLED = False

# Disable Telnet Console (enabled by default)
#TELNETCONSOLE_ENABLED = False

# Override the default request headers
DEFAULT_REQUEST_HEADERS = {
    "Accept-Encoding": "gzip, deflate",
    "accept": "text/html,application/xhtml+xml,application/xml",
    "accept-language": "en-US,en;q=0.9,de;q=0.8",
    "cache-control": "max-age=0",
    "priority":"u=0, i",
    "sec-ch-ua":'Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133',
    "sec-ch-ua-mobile":"?0",
    "sec-ch-ua-platform":"macOS",
    "sec-fetch-dest":"document",
    "sec-fetch-mode":"navigate",
    "sec-fetch-site":"none",
    "sec-fetch-user":"?1",
    "upgrade-insecure-requests":"1",
    "user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "X-UPS-WatsonSearch" : "V2F0c29uIFNlYXJjaCBzY3JhcGVy"
}

env_headers = os.getenv("CRAWLER_HEADERS")
if env_headers is not None:
    DEFAULT_REQUEST_HEADERS = env_headers
    print("Set crawler headers from environment variable")



# Enable or disable spider middlewares
# See https://docs.scrapy.org/en/latest/topics/spider-middleware.html
# SPIDER_MIDDLEWARES = {
#    "ups_ingest.middlewares.UpsIngestSpiderMiddleware": 543,
# }

# Enable or disable downloader middlewares
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
#DOWNLOADER_MIDDLEWARES = {
#    "ups_ingest.middlewares.UpsIngestDownloaderMiddleware": 543,
#}

# Enable or disable extensions
# See https://docs.scrapy.org/en/latest/topics/extensions.html
#EXTENSIONS = {
#    "scrapy.extensions.telnet.TelnetConsole": None,
#}

# Configure item pipelines
# See https://docs.scrapy.org/en/latest/topics/item-pipeline.html
#ITEM_PIPELINES = {
#    "ups_ingest.pipelines.UpsIngestPipeline": 300,
#}
    
# Enable and configure the AutoThrottle extension (disabled by default)
# See https://docs.scrapy.org/en/latest/topics/autothrottle.html
AUTOTHROTTLE_ENABLED = os.environ.get('AUTOTHROTTLE_ENABLED',"False").lower() in ['true', '1']


# The initial download delay
#AUTOTHROTTLE_START_DELAY = 5
# The maximum download delay to be set in case of high latencies
#AUTOTHROTTLE_MAX_DELAY = 60
# The average number of requests Scrapy should be sending in parallel to
# each remote server
#AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0
# Enable showing throttling stats for every response received:
#AUTOTHROTTLE_DEBUG = False

# Enable and configure HTTP caching (disabled by default)
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html#httpcache-middleware-settings
#HTTPCACHE_ENABLED = True
#HTTPCACHE_EXPIRATION_SECS = 0
#HTTPCACHE_DIR = "httpcache"
#HTTPCACHE_IGNORE_HTTP_CODES = []
#HTTPCACHE_STORAGE = "scrapy.extensions.httpcache.FilesystemCacheStorage"


# DOWNLOAD_HANDLERS = {
#     "http": "scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler",
#     "https": "scrapy_playwright.handler.ScrapyPlaywrightDownloadHandler",
# }

# Set settings whose default value is deprecated to a future-proof value
TWISTED_REACTOR = "twisted.internet.asyncioreactor.AsyncioSelectorReactor"
FEED_EXPORT_ENCODING = "utf-8"

# Enforce depth-first search for more consistent crawling results
DEPTH_PRIORITY = 1
SCHEDULER_DISK_QUEUE = "scrapy.squeues.PickleFifoDiskQueue"
SCHEDULER_MEMORY_QUEUE = "scrapy.squeues.FifoMemoryQueue"

LOCAL_SCRAPYD_LOGS_DIR = "/scrapyd/logs"
ENABLE_LOGPARSER = True