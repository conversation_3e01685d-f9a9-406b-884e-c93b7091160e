
import json
import os
from dotenv import load_dotenv

from wxdingest.utils.applog import get_logger
logger = get_logger(__name__)

'''
Handles the loading of the config file as well as the access of specific
config parameters.
'''

load_dotenv()
_config = None

def _init_config(path=None):
    global _config
    if _config is not None:
        return

    filepath = _get_default_path()
    if filepath is None or not os.path.isfile(filepath):
        # logger.info('Initializing empty config')
        _config = {}
        
    else:
        with open(filepath, 'r') as fin:
            _config = json.loads(fin.read())

def _get_default_path():
    """
    Returns the path to the config file.
    """
    filename = f"config.json"
    return filename

def get_parameter(parameter_path, default:any=None):
    """
    Main function to access config parameters.
    Preference is given to environment variables, and then to the config file.
    """
    _init_config()
    
    # Only retrieve the top level item if this is hierarchical
    par_arr = None
    if '.' in parameter_path:
        par_arr = parameter_path.split('.')
        parameter_name = par_arr[0]
    else:
        parameter_name = parameter_path
        
    # Retrieve the parameter value
    if parameter_name in os.environ:
        value = os.environ.get(parameter_name)
        if value.startswith("json:"):
            value = value[5:]
        return convert_to_typed_value(value)
    if parameter_name not in _config:
        # logger.info("Config parameter %s is not specified " % (parameter_name))
        return default
    else:
        val = _config[parameter_name]
        try:
            # We don't want to store sensitive data in the config file.
            # Here we store it in an environment variable instead.
            if val.startswith('<') and val.endswith('>') and len(val)>2:
                env_val = os.environ.get(val[1:-1])
                if env_val is not None:
                    return env_val
        except:
            pass
        if par_arr is None:
            return val
        else:
            # If this is an hierarchical key, we
            # try to resolve the entire path
            for key in par_arr[1:]:
                if key not in val:
                    logger.info(f'Could not find config path {parameter_path}')
                    return default
                val = val[key]
            return val

def convert_to_typed_value(value):
    """
    Parses parameter values and converts them to their
    respective type. This is necessary as Helm Chart Secrets
    are always expressed as strings.
    """
    if value is None:
        return value

    try:
        if isinstance(value, str):
            return json.loads(value)
        else:
            # We only need to convert string values
            # Others are already in their target type
            return value
    except:
        # if the above doesn't work, it's a string
        return value

def set_parameter(name, value):
    """
    Sets a config parameter so that it can be accessed from anywhere
    in the application.
    """
    _init_config()
    if isinstance(value, str):
        os.environ[name] = value
    else:
        os.environ[name] = "json:{0}".format(json.dumps(value))

def overwrite_from_args(args):
    """
    Writes command line paramters into the config so any parameter
    can be accessed the same way through the config. It adds any parameters
    that are missing and overwrites parameters that already exist.
    """
    try:
        for name, value in vars(args).iteritems():
            if value is not None:
                set_parameter(name, value)
    except:
        pass

    try:
        for name, value in vars(args).items():
            if value is not None:
                set_parameter(name, value)
    except:
        pass