FROM python:3.11-bookworm

<PERSON>N<PERSON> PYTHONUNBUFFERED=1

# <PERSON><PERSON> should show EST timezone
ENV TZ="US/Eastern"


RUN apt-get update
RUN apt-get install -y gcc vim curl supervisor pkg-config libxml2-dev libxmlsec1-dev libxmlsec1-openssl

# Force a specific xmlsec and lxml version or it will break things
RUN pip install xmlsec==1.3.14
RUN pip install lxml==5.2.1


#########################################
##           Supervisor                ##
#########################################
COPY container/supervisord.conf /etc/

#########################################
##             SCRAPYD                 ##
#########################################

# Install scrapyd and client for deploy
RUN pip install scrapyd scrapyd-client logparser supervisor-stdout

# Defines the name of the project
ADD container/scrapy.cfg /etc/scrapy.cfg

# Set up scrapyd environment
COPY container/scrapyd.conf /etc/scrapyd/

# Create all the configured scrapyd directories
RUN mkdir -p /scrapyd-eggs
# VOLUME /scrapyd /scrapyd

# Without this, it breaks in the GCP OCP
RUN chmod -R 777 /scrapyd-eggs

# Tell scrawpydweb were to find scrapyd
ENV SCRAPYD_SERVERS="localhost:6800"

#########################################
##           WXD INGEST                ##
#########################################

# Upload the UPS Ingest code and install dependencies
ADD wxd-ingest /wxd-ingest
ADD wxd-ingest/config /config
WORKDIR /wxd-ingest

# Install the UPS Ingest code globally so that the spiders can access it
RUN pip install .

# Build the egg file for the spiders
WORKDIR /wxd-ingest/wxdingest/ingest/deploy
RUN mkdir -p /scrapyd-eggs/ups_ingest
RUN scrapyd-deploy --include-dependencies --build-egg=/scrapyd-eggs/ups_ingest/ups_ingest.egg
RUN chmod -R 777 /wxd-ingest


#########################################
##            SCRAPYDWEB               ##
#########################################

# Copy customized scrapydweb to container and install it
ADD scrapydweb /scrapydweb
WORKDIR /scrapydweb
RUN pip install .

# Copy the scrapyd settings file
COPY container/scrapydweb_settings_v10.py /scrapydweb/
RUN chmod -R 777 /scrapydweb

# Make a directory to hold all the scrapydweb data
RUN mkdir -p /scrapyd/scrapydweb_data

# Needed for Kubernetes deployment
RUN chmod -R 777 /usr/local/lib/python3.11/site-packages

EXPOSE 5000

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisord.conf"]