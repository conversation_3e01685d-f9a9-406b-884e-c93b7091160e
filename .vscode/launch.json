{"version": "002.0", "configurations": [{"name": "Ingest", "type": "python", "request": "launch", "program": "${workspaceFolder}/wxdingest/ingest/ingest_controller.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${cwd}"}}, {"name": "Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${cwd}"}}]}