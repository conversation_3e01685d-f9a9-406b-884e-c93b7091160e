{
    "python.defaultInterpreterPath": "${workspaceFolder}/env/bin/python",
    "python.testing.unittestArgs": [
        "-v",
        "-s",
        "./tests",
        "-p",
        "*test.py"
    ],
    "python.testing.pytestEnabled": false,
    "python.testing.unittestEnabled": true,
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/*.pyc": {
            "when": "$(basename).py"
        },
        "**/__pycache__": true,
        "**/app.egg-info": true,
        "**/env": true,
        "**/.env.dist": true,
        "**/*.log": true,
        "**/.0": true,
    },
    "workbench.colorCustomizations": {
        "tab.activeBorder": "#ff0000",
        "tab.unfocusedActiveBorder": "#000000",
        "tab.activeBackground": "#045980"
    },
    "workbench.editor.wrapTabs": true,
    "debug.toolBarLocation": "docked",
    "python.formatting.provider": "autopep8",
    "editor.formatOnSave": true,
    "[python]": {
        "editor.defaultFormatter": "ms-python.autopep8"
    },
    "python.REPL.enableREPLSmartSend": false
}